# 🦌 DeerFlow

[![Python 3.12+](https://img.shields.io/badge/python-3.12+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![DeepWiki](https://img.shields.io/badge/DeepWiki-bytedance%2Fdeer--flow-blue.svg?logo=data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAyCAYAAAAnWDnqAAAAAXNSR0IArs4c6QAAA05JREFUaEPtmUtyEzEQhtWTQyQLHNak2AB7ZnyXZMEjXMGeK/AIi+QuHrMnbChYY7MIh8g01fJoopFb0uhhEqqcbWTp06/uv1saEDv4O3n3dV60RfP947Mm9/SQc0ICFQgzfc4CYZoTPAswgSJCCUJUnAAoRHOAUOcATwbmVLWdGoH//PB8mnKqScAhsD0kYP3j/Yt5LPQe2KvcXmGvRHcDnpxfL2zOYJ1mFwrryWTz0advv1Ut4CJgf5uhDuDj5eUcAUoahrdY/56ebRWeraTjMt/00Sh3UDtjgHtQNHwcRGOC98BJEAEymycmYcWwOprTgcB6VZ5JK5TAJ+fXGLBm3FDAmn6oPPjR4rKCAoJCal2eAiQp2x0vxTPB3ALO2CRkwmDy5WohzBDwSEFKRwPbknEggCPB/imwrycgxX2NzoMCHhPkDwqYMr9tRcP5qNrMZHkVnOjRMWwLCcr8ohBVb1OMjxLwGCvjTikrsBOiA6fNyCrm8V1rP93iVPpwaE+gO0SsWmPiXB+jikdf6SizrT5qKasx5j8ABbHpFTx+vFXp9EnYQmLx02h1QTTrl6eDqxLnGjporxl3NL3agEvXdT0WmEost648sQOYAeJS9Q7bfUVoMGnjo4AZdUMQku50McCcMWcBPvr0SzbTAFDfvJqwLzgxwATnCgnp4wDl6Aa+Ax283gghmj+vj7feE2KBBRMW3FzOpLOADl0Isb5587h/U4gGvkt5v60Z1VLG8BhYjbzRwyQZemwAd6cCR5/XFWLYZRIMpX39AR0tjaGGiGzLVyhse5C9RKC6ai42ppWPKiBagOvaYk8lO7DajerabOZP46Lby5wKjw1HCRx7p9sVMOWGzb/vA1hwiWc6jm3MvQDTogQkiqIhJV0nBQBTU+3okKCFDy9WwferkHjtxib7t3xIUQtHxnIwtx4mpg26/HfwVNVDb4oI9RHmx5WGelRVlrtiw43zboCLaxv46AZeB3IlTkwouebTr1y2NjSpHz68WNFjHvupy3q8TFn3Hos2IAk4Ju5dCo8B3wP7VPr/FGaKiG+T+v+TQqIrOqMTL1VdWV1DdmcbO8KXBz6esmYWYKPwDL5b5FA1a0hwapHiom0r/cKaoqr+27/XcrS5UwSMbQAAAABJRU5ErkJggg==)](https://deepwiki.com/bytedance/deer-flow)
<!-- DeepWiki badge generated by https://deepwiki.ryoppippi.com/ -->

[English](./README.md) | [简体中文](./README_zh.md) | [日本語](./README_ja.md) | [Deutsch](./README_de.md) | [Español](./README_es.md) | [Русский](./README_ru.md) | [Portuguese](./README_pt.md)

> Создано на базе открытого кода, возвращено в открытый код.

**DeerFlow** (**D**eep **E**xploration and **E**fficient **R**esearch **Flow**) - это фреймворк для глубокого исследования, разработанный сообществом и основанный на впечатляющей работе сообщества открытого кода. Наша цель - объединить языковые модели со специализированными инструментами для таких задач, как веб-поиск, сканирование и выполнение кода Python, одновременно возвращая пользу сообществу, которое сделало это возможным.

В настоящее время DeerFlow официально вошел в Центр приложений FaaS Volcengine. Пользователи могут испытать его онлайн через ссылку для опыта, чтобы интуитивно почувствовать его мощные функции и удобные операции. В то же время, для удовлетворения потребностей развертывания различных пользователей, DeerFlow поддерживает развертывание одним кликом на основе Volcengine. Нажмите на ссылку развертывания, чтобы быстро завершить процесс развертывания и начать эффективное исследовательское путешествие.

Пожалуйста, посетите [наш официальный сайт](https://deerflow.tech/) для получения дополнительной информации.

## Демонстрация

### Видео

<https://github.com/user-attachments/assets/f3786598-1f2a-4d07-919e-8b99dfa1de3e>

В этой демонстрации мы показываем, как использовать DeerFlow для:

- Бесшовной интеграции с сервисами MCP
- Проведения процесса глубокого исследования и создания комплексного отчета с изображениями
- Создания аудио подкаста на основе сгенерированного отчета

### Повторы

- [Какова высота Эйфелевой башни по сравнению с самым высоким зданием?](https://deerflow.tech/chat?replay=eiffel-tower-vs-tallest-building)
- [Какие репозитории самые популярные на GitHub?](https://deerflow.tech/chat?replay=github-top-trending-repo)
- [Написать статью о традиционных блюдах Нанкина](https://deerflow.tech/chat?replay=nanjing-traditional-dishes)
- [Как украсить съемную квартиру?](https://deerflow.tech/chat?replay=rental-apartment-decoration)
- [Посетите наш официальный сайт, чтобы изучить больше повторов.](https://deerflow.tech/#case-studies)

---

## 📑 Оглавление

- [🚀 Быстрый старт](#быстрый-старт)
- [🌟 Особенности](#особенности)
- [🏗️ Архитектура](#архитектура)
- [🛠️ Разработка](#разработка)
- [🐳 Docker](#docker)
- [🗣️ Интеграция преобразования текста в речь](#интеграция-преобразования-текста-в-речь)
- [📚 Примеры](#примеры)
- [❓ FAQ](#faq)
- [📜 Лицензия](#лицензия)
- [💖 Благодарности](#благодарности)
- [⭐ История звезд](#история-звезд)

## Быстрый старт

DeerFlow разработан на Python и поставляется с веб-интерфейсом, написанным на Node.js. Для обеспечения плавного процесса настройки мы рекомендуем использовать следующие инструменты:

### Рекомендуемые инструменты

- **[`uv`](https://docs.astral.sh/uv/getting-started/installation/):**
  Упрощает управление средой Python и зависимостями. `uv` автоматически создает виртуальную среду в корневом каталоге и устанавливает все необходимые пакеты за вас—без необходимости вручную устанавливать среды Python.

- **[`nvm`](https://github.com/nvm-sh/nvm):**
  Легко управляйте несколькими версиями среды выполнения Node.js.

- **[`pnpm`](https://pnpm.io/installation):**
  Установка и управление зависимостями проекта Node.js.

### Требования к среде

Убедитесь, что ваша система соответствует следующим минимальным требованиям:

- **[Python](https://www.python.org/downloads/):** Версия `3.12+`
- **[Node.js](https://nodejs.org/en/download/):** Версия `22+`

### Установка

```bash
# Клонировать репозиторий
git clone https://github.com/bytedance/deer-flow.git
cd deer-flow

# Установить зависимости, uv позаботится об интерпретаторе python и создании venv, и установит необходимые пакеты
uv sync

# Настроить .env с вашими API-ключами
# Tavily: https://app.tavily.com/home
# Brave_SEARCH: https://brave.com/search/api/
# volcengine TTS: Добавьте ваши учетные данные TTS, если они у вас есть
cp .env.example .env

# См. разделы 'Поддерживаемые поисковые системы' и 'Интеграция преобразования текста в речь' ниже для всех доступных опций

# Настроить conf.yaml для вашей модели LLM и API-ключей
# Пожалуйста, обратитесь к 'docs/configuration_guide.md' для получения дополнительной информации
cp conf.yaml.example conf.yaml

# Установить marp для генерации презентаций
# https://github.com/marp-team/marp-cli?tab=readme-ov-file#use-package-manager
brew install marp-cli
```

По желанию установите зависимости веб-интерфейса через [pnpm](https://pnpm.io/installation):

```bash
cd deer-flow/web
pnpm install
```

### Конфигурации

Пожалуйста, обратитесь к [Руководству по конфигурации](docs/configuration_guide.md) для получения дополнительной информации.

> [!ПРИМЕЧАНИЕ]
> Прежде чем запустить проект, внимательно прочитайте руководство и обновите конфигурации в соответствии с вашими конкретными настройками и требованиями.

### Консольный интерфейс

Самый быстрый способ запустить проект - использовать консольный интерфейс.

```bash
# Запустить проект в оболочке, похожей на bash
uv run main.py
```

### Веб-интерфейс

Этот проект также включает веб-интерфейс, предлагающий более динамичный и привлекательный интерактивный опыт.

> [!ПРИМЕЧАНИЕ]
> Сначала вам нужно установить зависимости веб-интерфейса.

```bash
# Запустить оба сервера, бэкенд и фронтенд, в режиме разработки
# На macOS/Linux
./bootstrap.sh -d

# На Windows
bootstrap.bat -d
```

Откройте ваш браузер и посетите [`http://localhost:3000`](http://localhost:3000), чтобы исследовать веб-интерфейс.

Исследуйте больше деталей в каталоге [`web`](./web/).

## Поддерживаемые поисковые системы

DeerFlow поддерживает несколько поисковых систем, которые можно настроить в файле `.env` с помощью переменной `SEARCH_API`:

- **Tavily** (по умолчанию): Специализированный поисковый API для приложений ИИ

  - Требуется `TAVILY_API_KEY` в вашем файле `.env`
  - Зарегистрируйтесь на: <https://app.tavily.com/home>

- **DuckDuckGo**: Поисковая система, ориентированная на конфиденциальность

  - Не требуется API-ключ

- **Brave Search**: Поисковая система, ориентированная на конфиденциальность, с расширенными функциями

  - Требуется `BRAVE_SEARCH_API_KEY` в вашем файле `.env`
  - Зарегистрируйтесь на: <https://brave.com/search/api/>

- **Arxiv**: Поиск научных статей для академических исследований
  - Не требуется API-ключ
  - Специализируется на научных и академических статьях

Чтобы настроить предпочитаемую поисковую систему, установите переменную `SEARCH_API` в вашем файле `.env`:

```bash
# Выберите одно: tavily, duckduckgo, brave_search, arxiv
SEARCH_API=tavily
```

## Особенности

### Ключевые возможности

- 🤖 **Интеграция LLM**
  - Поддерживает интеграцию большинства моделей через [litellm](https://docs.litellm.ai/docs/providers).
  - Поддержка моделей с открытым исходным кодом, таких как Qwen
  - API-интерфейс, совместимый с OpenAI
  - Многоуровневая система LLM для задач различной сложности

### Инструменты и интеграции MCP

- 🔍 **Поиск и извлечение**

  - Веб-поиск через Tavily, Brave Search и другие
  - Сканирование с Jina
  - Расширенное извлечение контента

- 🔗 **Бесшовная интеграция MCP**
  - Расширение возможностей для доступа к частным доменам, графам знаний, веб-браузингу и многому другому
  - Облегчает интеграцию различных исследовательских инструментов и методологий

### Человеческое взаимодействие

- 🧠 **Человек в контуре**

  - Поддерживает интерактивное изменение планов исследования с использованием естественного языка
  - Поддерживает автоматическое принятие планов исследования

- 📝 **Пост-редактирование отчетов**
  - Поддерживает блочное редактирование в стиле Notion
  - Позволяет совершенствовать с помощью ИИ, включая полировку, сокращение и расширение предложений
  - Работает на [tiptap](https://tiptap.dev/)

### Создание контента

- 🎙️ **Генерация подкастов и презентаций**
  - Генерация сценариев подкастов и синтез аудио с помощью ИИ
  - Автоматическое создание простых презентаций PowerPoint
  - Настраиваемые шаблоны для индивидуального контента

## Архитектура

DeerFlow реализует модульную архитектуру системы с несколькими агентами, предназначенную для автоматизированных исследований и анализа кода. Система построена на LangGraph, обеспечивающей гибкий рабочий процесс на основе состояний, где компоненты взаимодействуют через четко определенную систему передачи сообщений.

![Диаграмма архитектуры](./assets/architecture.png)

> Посмотрите вживую на [deerflow.tech](https://deerflow.tech/#multi-agent-architecture)

В системе используется оптимизированный рабочий процесс со следующими компонентами:

1. **Координатор**: Точка входа, управляющая жизненным циклом рабочего процесса

   - Инициирует процесс исследования на основе пользовательского ввода
   - Делегирует задачи планировщику, когда это необходимо
   - Выступает в качестве основного интерфейса между пользователем и системой

2. **Планировщик**: Стратегический компонент для декомпозиции и планирования задач

   - Анализирует цели исследования и создает структурированные планы выполнения
   - Определяет, достаточно ли доступного контекста или требуется дополнительное исследование
   - Управляет потоком исследования и решает, когда генерировать итоговый отчет

3. **Исследовательская команда**: Набор специализированных агентов, которые выполняют план:

   - **Исследователь**: Проводит веб-поиск и сбор информации с использованием таких инструментов, как поисковые системы, сканирование и даже сервисы MCP.
   - **Программист**: Обрабатывает анализ кода, выполнение и технические задачи с помощью инструмента Python REPL.
     Каждый агент имеет доступ к определенным инструментам, оптимизированным для его роли, и работает в рамках фреймворка LangGraph

4. **Репортер**: Процессор финальной стадии для результатов исследования
   - Агрегирует находки исследовательской команды
   - Обрабатывает и структурирует собранную информацию
   - Генерирует комплексные исследовательские отчеты

## Интеграция преобразования текста в речь

DeerFlow теперь включает функцию преобразования текста в речь (TTS), которая позволяет конвертировать исследовательские отчеты в речь. Эта функция использует API TTS volcengine для генерации высококачественного аудио из текста. Также можно настраивать такие параметры, как скорость, громкость и тон.

### Использование API TTS

Вы можете получить доступ к функциональности TTS через конечную точку `/api/tts`:

```bash
# Пример вызова API с использованием curl
curl --location 'http://localhost:8000/api/tts' \
--header 'Content-Type: application/json' \
--data '{
    "text": "Это тест функциональности преобразования текста в речь.",
    "speed_ratio": 1.0,
    "volume_ratio": 1.0,
    "pitch_ratio": 1.0
}' \
--output speech.mp3
```

## Разработка

### Тестирование

Запустите набор тестов:

```bash
# Запустить все тесты
make test

# Запустить определенный тестовый файл
pytest tests/integration/test_workflow.py

# Запустить с покрытием
make coverage
```

### Качество кода

```bash
# Запустить линтинг
make lint

# Форматировать код
make format
```

### Отладка с LangGraph Studio

DeerFlow использует LangGraph для своей архитектуры рабочего процесса. Вы можете использовать LangGraph Studio для отладки и визуализации рабочего процесса в реальном времени.

#### Запуск LangGraph Studio локально

DeerFlow включает конфигурационный файл `langgraph.json`, который определяет структуру графа и зависимости для LangGraph Studio. Этот файл указывает на графы рабочего процесса, определенные в проекте, и автоматически загружает переменные окружения из файла `.env`.

##### Mac

```bash
# Установите менеджер пакетов uv, если у вас его нет
curl -LsSf https://astral.sh/uv/install.sh | sh

# Установите зависимости и запустите сервер LangGraph
uvx --refresh --from "langgraph-cli[inmem]" --with-editable . --python 3.12 langgraph dev --allow-blocking
```

##### Windows / Linux

```bash
# Установить зависимости
pip install -e .
pip install -U "langgraph-cli[inmem]"

# Запустить сервер LangGraph
langgraph dev
```

После запуска сервера LangGraph вы увидите несколько URL в терминале:

- API: <http://127.0.0.1:2024>
- Studio UI: <https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024>
- API Docs: <http://127.0.0.1:2024/docs>

Откройте ссылку Studio UI в вашем браузере для доступа к интерфейсу отладки.

#### Использование LangGraph Studio

В интерфейсе Studio вы можете:

1. Визуализировать граф рабочего процесса и видеть, как соединяются компоненты
2. Отслеживать выполнение в реальном времени, чтобы видеть, как данные проходят через систему
3. Исследовать состояние на каждом шаге рабочего процесса
4. Отлаживать проблемы путем изучения входов и выходов каждого компонента
5. Предоставлять обратную связь во время фазы планирования для уточнения планов исследования

Когда вы отправляете тему исследования в интерфейсе Studio, вы сможете увидеть весь процесс выполнения рабочего процесса, включая:

- Фазу планирования, где создается план исследования
- Цикл обратной связи, где вы можете модифицировать план
- Фазы исследования и написания для каждого раздела
- Генерацию итогового отчета

### Включение трассировки LangSmith

DeerFlow поддерживает трассировку LangSmith, чтобы помочь вам отладить и контролировать ваши рабочие процессы. Чтобы включить трассировку LangSmith:

1. Убедитесь, что в вашем файле `.env` есть следующие конфигурации (см. `.env.example`):

   ```bash
   LANGSMITH_TRACING=true
   LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
   LANGSMITH_API_KEY="xxx"
   LANGSMITH_PROJECT="xxx"
   ```

2. Запустите трассировку и визуализируйте граф локально с LangSmith, выполнив:

   ```bash
   langgraph dev
   ```

Это включит визуализацию трассировки в LangGraph Studio и отправит ваши трассировки в LangSmith для мониторинга и анализа.

## Docker

Вы также можете запустить этот проект с Docker.

Во-первых, вам нужно прочитать [конфигурацию](docs/configuration_guide.md) ниже. Убедитесь, что файлы `.env`, `.conf.yaml` готовы.

Во-вторых, чтобы построить Docker-образ вашего собственного веб-сервера:

```bash
docker build -t deer-flow-api .
```

Наконец, запустите Docker-контейнер с веб-сервером:

```bash
# Замените deer-flow-api-app на предпочитаемое вами имя контейнера
docker run -d -t -p 8000:8000 --env-file .env --name deer-flow-api-app deer-flow-api

# остановить сервер
docker stop deer-flow-api-app
```

### Docker Compose (включает как бэкенд, так и фронтенд)

DeerFlow предоставляет настройку docker-compose для легкого запуска бэкенда и фронтенда вместе:

```bash
# сборка docker-образа
docker compose build

# запуск сервера
docker compose up
```

## Примеры

Следующие примеры демонстрируют возможности DeerFlow:

### Исследовательские отчеты

1. **Отчет о OpenAI Sora** - Анализ инструмента ИИ Sora от OpenAI

   - Обсуждаются функции, доступ, инженерия промптов, ограничения и этические соображения
   - [Просмотреть полный отчет](examples/openai_sora_report.md)

2. **Отчет о протоколе Agent to Agent от Google** - Обзор протокола Agent to Agent (A2A) от Google

   - Обсуждается его роль в коммуникации агентов ИИ и его отношение к протоколу Model Context Protocol (MCP) от Anthropic
   - [Просмотреть полный отчет](examples/what_is_agent_to_agent_protocol.md)

3. **Что такое MCP?** - Комплексный анализ термина "MCP" в различных контекстах

   - Исследует Model Context Protocol в ИИ, Монокальцийфосфат в химии и Микроканальные пластины в электронике
   - [Просмотреть полный отчет](examples/what_is_mcp.md)

4. **Колебания цены Биткоина** - Анализ недавних движений цены Биткоина

   - Исследует рыночные тренды, регуляторные влияния и технические индикаторы
   - Предоставляет рекомендации на основе исторических данных
   - [Просмотреть полный отчет](examples/bitcoin_price_fluctuation.md)

5. **Что такое LLM?** - Углубленное исследование больших языковых моделей

   - Обсуждаются архитектура, обучение, приложения и этические соображения
   - [Просмотреть полный отчет](examples/what_is_llm.md)

6. **Как использовать Claude для глубокого исследования?** - Лучшие практики и рабочие процессы для использования Claude в глубоком исследовании

   - Охватывает инженерию промптов, анализ данных и интеграцию с другими инструментами
   - [Просмотреть полный отчет](examples/how_to_use_claude_deep_research.md)

7. **Внедрение ИИ в здравоохранении: Влияющие факторы** - Анализ факторов, движущих внедрением ИИ в здравоохранении

   - Обсуждаются технологии ИИ, качество данных, этические соображения, экономические оценки, организационная готовность и цифровая инфраструктура
   - [Просмотреть полный отчет](examples/AI_adoption_in_healthcare.md)

8. **Влияние квантовых вычислений на криптографию** - Анализ влияния квантовых вычислений на криптографию

   - Обсуждаются уязвимости классической криптографии, пост-квантовая криптография и криптографические решения, устойчивые к квантовым вычислениям
   - [Просмотреть полный отчет](examples/Quantum_Computing_Impact_on_Cryptography.md)

9. **Ключевые моменты выступлений Криштиану Роналду** - Анализ выдающихся выступлений Криштиану Роналду
   - Обсуждаются его карьерные достижения, международные голы и выступления в различных матчах
   - [Просмотреть полный отчет](examples/Cristiano_Ronaldo's_Performance_Highlights.md)

Чтобы запустить эти примеры или создать собственные исследовательские отчеты, вы можете использовать следующие команды:

```bash
# Запустить с определенным запросом
uv run main.py "Какие факторы влияют на внедрение ИИ в здравоохранении?"

# Запустить с пользовательскими параметрами планирования
uv run main.py --max_plan_iterations 3 "Как квантовые вычисления влияют на криптографию?"

# Запустить в интерактивном режиме с встроенными вопросами
uv run main.py --interactive

# Или запустить с базовым интерактивным приглашением
uv run main.py

# Посмотреть все доступные опции 
uv run main.py --help
```

### Интерактивный режим

Приложение теперь поддерживает интерактивный режим с встроенными вопросами как на английском, так и на китайском языках:

1. Запустите интерактивный режим:

   ```bash
   uv run main.py --interactive
   ```

2. Выберите предпочитаемый язык (English или 中文)

3. Выберите из списка встроенных вопросов или выберите опцию задать собственный вопрос

4. Система обработает ваш вопрос и сгенерирует комплексный исследовательский отчет

### Человек в контуре

DeerFlow включает механизм "человек в контуре", который позволяет вам просматривать, редактировать и утверждать планы исследования перед их выполнением:

1. **Просмотр плана**: Когда активирован режим "человек в контуре", система представит сгенерированный план исследования для вашего просмотра перед выполнением

2. **Предоставление обратной связи**: Вы можете:

   - Принять план, ответив `[ACCEPTED]`
   - Отредактировать план, предоставив обратную связь (например, `[EDIT PLAN] Добавить больше шагов о технической реализации`)
   - Система включит вашу обратную связь и сгенерирует пересмотренный план

3. **Автоматическое принятие**: Вы можете включить автоматическое принятие, чтобы пропустить процесс просмотра:

   - Через API: Установите `auto_accepted_plan: true` в вашем запросе

4. **Интеграция API**: При использовании API вы можете предоставить обратную связь через параметр `feedback`:

   ```json
   {
     "messages": [{ "role": "user", "content": "Что такое квантовые вычисления?" }],
     "thread_id": "my_thread_id",
     "auto_accepted_plan": false,
     "feedback": "[EDIT PLAN] Включить больше о квантовых алгоритмах"
   }
   ```

### Аргументы командной строки

Приложение поддерживает несколько аргументов командной строки для настройки его поведения:

- **query**: Запрос исследования для обработки (может состоять из нескольких слов)
- **--interactive**: Запустить в интерактивном режиме с встроенными вопросами
- **--max_plan_iterations**: Максимальное количество циклов планирования (по умолчанию: 1)
- **--max_step_num**: Максимальное количество шагов в плане исследования (по умолчанию: 3)
- **--debug**: Включить подробное логирование отладки

## FAQ

Пожалуйста, обратитесь к [FAQ.md](docs/FAQ.md) для получения дополнительной информации.

## Лицензия

Этот проект имеет открытый исходный код и доступен под [Лицензией MIT](./LICENSE).

## Благодарности

DeerFlow создан на основе невероятной работы сообщества открытого кода. Мы глубоко благодарны всем проектам и контрибьюторам, чьи усилия сделали DeerFlow возможным. Поистине, мы стоим на плечах гигантов.

Мы хотели бы выразить искреннюю признательность следующим проектам за их неоценимый вклад:

- **[LangChain](https://github.com/langchain-ai/langchain)**: Их исключительный фреймворк обеспечивает наши взаимодействия и цепочки LLM, позволяя бесшовную интеграцию и функциональность.
- **[LangGraph](https://github.com/langchain-ai/langgraph)**: Их инновационный подход к оркестровке многоагентных систем сыграл решающую роль в обеспечении сложных рабочих процессов DeerFlow.

Эти проекты являются примером преобразующей силы сотрудничества в области открытого кода, и мы гордимся тем, что строим на их основе.

### Ключевые контрибьюторы

Сердечная благодарность основным авторам `DeerFlow`, чье видение, страсть и преданность делу вдохнули жизнь в этот проект:

- **[Daniel Walnut](https://github.com/hetaoBackend/)**
- **[Henry Li](https://github.com/magiccube/)**

Ваша непоколебимая приверженность и опыт стали движущей силой успеха DeerFlow. Мы считаем за честь иметь вас во главе этого путешествия.

## История звезд

[![Star History Chart](https://api.star-history.com/svg?repos=bytedance/deer-flow&type=Date)](https://star-history.com/#bytedance/deer-flow&Date)

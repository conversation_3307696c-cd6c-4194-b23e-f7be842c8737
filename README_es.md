# 🦌 DeerFlow

[![Python 3.12+](https://img.shields.io/badge/python-3.12+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![DeepWiki](https://img.shields.io/badge/DeepWiki-bytedance%2Fdeer--flow-blue.svg?logo=data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACwAAAAyCAYAAAAnWDnqAAAAAXNSR0IArs4c6QAAA05JREFUaEPtmUtyEzEQhtWTQyQLHNak2AB7ZnyXZMEjXMGeK/AIi+QuHrMnbChYY7MIh8g01fJoopFb0uhhEqqcbWTp06/uv1saEDv4O3n3dV60RfP947Mm9/SQc0ICFQgzfc4CYZoTPAswgSJCCUJUnAAoRHOAUOcATwbmVLWdGoH//PB8mnKqScAhsD0kYP3j/Yt5LPQe2KvcXmGvRHcDnpxfL2zOYJ1mFwrryWTz0advv1Ut4CJgf5uhDuDj5eUcAUoahrdY/56ebRWeraTjMt/00Sh3UDtjgHtQNHwcRGOC98BJEAEymycmYcWwOprTgcB6VZ5JK5TAJ+fXGLBm3FDAmn6oPPjR4rKCAoJCal2eAiQp2x0vxTPB3ALO2CRkwmDy5WohzBDwSEFKRwPbknEggCPB/imwrycgxX2NzoMCHhPkDwqYMr9tRcP5qNrMZHkVnOjRMWwLCcr8ohBVb1OMjxLwGCvjTikrsBOiA6fNyCrm8V1rP93iVPpwaE+gO0SsWmPiXB+jikdf6SizrT5qKasx5j8ABbHpFTx+vFXp9EnYQmLx02h1QTTrl6eDqxLnGjporxl3NL3agEvXdT0WmEost648sQOYAeJS9Q7bfUVoMGnjo4AZdUMQku50McCcMWcBPvr0SzbTAFDfvJqwLzgxwATnCgnp4wDl6Aa+Ax283gghmj+vj7feE2KBBRMW3FzOpLOADl0Isb5587h/U4gGvkt5v60Z1VLG8BhYjbzRwyQZemwAd6cCR5/XFWLYZRIMpX39AR0tjaGGiGzLVyhse5C9RKC6ai42ppWPKiBagOvaYk8lO7DajerabOZP46Lby5wKjw1HCRx7p9sVMOWGzb/vA1hwiWc6jm3MvQDTogQkiqIhJV0nBQBTU+3okKCFDy9WwferkHjtxib7t3xIUQtHxnIwtx4mpg26/HfwVNVDb4oI9RHmx5WGelRVlrtiw43zboCLaxv46AZeB3IlTkwouebTr1y2NjSpHz68WNFjHvupy3q8TFn3Hos2IAk4Ju5dCo8B3wP7VPr/FGaKiG+T+v+TQqIrOqMTL1VdWV1DdmcbO8KXBz6esmYWYKPwDL5b5FA1a0hwapHiom0r/cKaoqr+27/XcrS5UwSMbQAAAABJRU5ErkJggg==)](https://deepwiki.com/bytedance/deer-flow)
<!-- DeepWiki badge generated by https://deepwiki.ryoppippi.com/ -->

[English](./README.md) | [简体中文](./README_zh.md) | [日本語](./README_ja.md) | [Deutsch](./README_de.md) | [Español](./README_es.md) | [Русский](./README_ru.md)  | [Portuguese](./README_pt.md)

> Originado del código abierto, retribuido al código abierto.

**DeerFlow** (**D**eep **E**xploration and **E**fficient **R**esearch **Flow**) es un marco de Investigación Profunda impulsado por la comunidad que se basa en el increíble trabajo de la comunidad de código abierto. Nuestro objetivo es combinar modelos de lenguaje con herramientas especializadas para tareas como búsqueda web, rastreo y ejecución de código Python, mientras devolvemos a la comunidad que hizo esto posible.

Actualmente, DeerFlow ha ingresado oficialmente al Centro de Aplicaciones FaaS de Volcengine. Los usuarios pueden experimentarlo en línea a través del enlace de experiencia para sentir intuitivamente sus potentes funciones y operaciones convenientes. Al mismo tiempo, para satisfacer las necesidades de implementación de diferentes usuarios, DeerFlow admite la implementación con un clic basada en Volcengine. Haga clic en el enlace de implementación para completar rápidamente el proceso de implementación y comenzar un viaje de investigación eficiente.

Por favor, visita [nuestra página web oficial](https://deerflow.tech/) para más detalles.

## Demostración

### Video

<https://github.com/user-attachments/assets/f3786598-1f2a-4d07-919e-8b99dfa1de3e>

En esta demostración, mostramos cómo usar DeerFlow para:

- Integrar perfectamente con servicios MCP
- Realizar el proceso de Investigación Profunda y producir un informe completo con imágenes
- Crear audio de podcast basado en el informe generado

### Repeticiones

- [¿Qué altura tiene la Torre Eiffel comparada con el edificio más alto?](https://deerflow.tech/chat?replay=eiffel-tower-vs-tallest-building)
- [¿Cuáles son los repositorios más populares en GitHub?](https://deerflow.tech/chat?replay=github-top-trending-repo)
- [Escribir un artículo sobre los platos tradicionales de Nanjing](https://deerflow.tech/chat?replay=nanjing-traditional-dishes)
- [¿Cómo decorar un apartamento de alquiler?](https://deerflow.tech/chat?replay=rental-apartment-decoration)
- [Visita nuestra página web oficial para explorar más repeticiones.](https://deerflow.tech/#case-studies)

---

## 📑 Tabla de Contenidos

- [🚀 Inicio Rápido](#inicio-rápido)
- [🌟 Características](#características)
- [🏗️ Arquitectura](#arquitectura)
- [🛠️ Desarrollo](#desarrollo)
- [🐳 Docker](#docker)
- [🗣️ Integración de Texto a Voz](#integración-de-texto-a-voz)
- [📚 Ejemplos](#ejemplos)
- [❓ Preguntas Frecuentes](#preguntas-frecuentes)
- [📜 Licencia](#licencia)
- [💖 Agradecimientos](#agradecimientos)
- [⭐ Historial de Estrellas](#historial-de-estrellas)

## Inicio Rápido

DeerFlow está desarrollado en Python y viene con una interfaz web escrita en Node.js. Para garantizar un proceso de configuración sin problemas, recomendamos utilizar las siguientes herramientas:

### Herramientas Recomendadas

- **[`uv`](https://docs.astral.sh/uv/getting-started/installation/):**
  Simplifica la gestión del entorno Python y las dependencias. `uv` crea automáticamente un entorno virtual en el directorio raíz e instala todos los paquetes necesarios por ti—sin necesidad de instalar entornos Python manualmente.

- **[`nvm`](https://github.com/nvm-sh/nvm):**
  Gestiona múltiples versiones del entorno de ejecución Node.js sin esfuerzo.

- **[`pnpm`](https://pnpm.io/installation):**
  Instala y gestiona dependencias del proyecto Node.js.

### Requisitos del Entorno

Asegúrate de que tu sistema cumple con los siguientes requisitos mínimos:

- **[Python](https://www.python.org/downloads/):** Versión `3.12+`
- **[Node.js](https://nodejs.org/en/download/):** Versión `22+`

### Instalación

```bash
# Clonar el repositorio
git clone https://github.com/bytedance/deer-flow.git
cd deer-flow

# Instalar dependencias, uv se encargará del intérprete de python, la creación del entorno virtual y la instalación de los paquetes necesarios
uv sync

# Configurar .env con tus claves API
# Tavily: https://app.tavily.com/home
# Brave_SEARCH: https://brave.com/search/api/
# volcengine TTS: Añade tus credenciales TTS si las tienes
cp .env.example .env

# Ver las secciones 'Motores de Búsqueda Compatibles' e 'Integración de Texto a Voz' a continuación para todas las opciones disponibles

# Configurar conf.yaml para tu modelo LLM y claves API
# Por favor, consulta 'docs/configuration_guide.md' para más detalles
cp conf.yaml.example conf.yaml

# Instalar marp para la generación de presentaciones
# https://github.com/marp-team/marp-cli?tab=readme-ov-file#use-package-manager
brew install marp-cli
```

Opcionalmente, instala las dependencias de la interfaz web vía [pnpm](https://pnpm.io/installation):

```bash
cd deer-flow/web
pnpm install
```

### Configuraciones

Por favor, consulta la [Guía de Configuración](docs/configuration_guide.md) para más detalles.

> [!NOTA]
> Antes de iniciar el proyecto, lee la guía cuidadosamente y actualiza las configuraciones para que coincidan con tus ajustes y requisitos específicos.

### Interfaz de Consola

La forma más rápida de ejecutar el proyecto es utilizar la interfaz de consola.

```bash
# Ejecutar el proyecto en un shell tipo bash
uv run main.py
```

### Interfaz Web

Este proyecto también incluye una Interfaz Web, que ofrece una experiencia interactiva más dinámica y atractiva.

> [!NOTA]
> Necesitas instalar primero las dependencias de la interfaz web.

```bash
# Ejecutar tanto el servidor backend como el frontend en modo desarrollo
# En macOS/Linux
./bootstrap.sh -d

# En Windows
bootstrap.bat -d
```

Abre tu navegador y visita [`http://localhost:3000`](http://localhost:3000) para explorar la interfaz web.

Explora más detalles en el directorio [`web`](./web/).

## Motores de Búsqueda Compatibles

DeerFlow soporta múltiples motores de búsqueda que pueden configurarse en tu archivo `.env` usando la variable `SEARCH_API`:

- **Tavily** (predeterminado): Una API de búsqueda especializada para aplicaciones de IA

  - Requiere `TAVILY_API_KEY` en tu archivo `.env`
  - Regístrate en: <https://app.tavily.com/home>

- **DuckDuckGo**: Motor de búsqueda centrado en la privacidad

  - No requiere clave API

- **Brave Search**: Motor de búsqueda centrado en la privacidad con características avanzadas

  - Requiere `BRAVE_SEARCH_API_KEY` en tu archivo `.env`
  - Regístrate en: <https://brave.com/search/api/>

- **Arxiv**: Búsqueda de artículos científicos para investigación académica
  - No requiere clave API
  - Especializado en artículos científicos y académicos

Para configurar tu motor de búsqueda preferido, establece la variable `SEARCH_API` en tu archivo `.env`:

```bash
# Elige uno: tavily, duckduckgo, brave_search, arxiv
SEARCH_API=tavily
```

## Características

### Capacidades Principales

- 🤖 **Integración de LLM**
  - Soporta la integración de la mayoría de los modelos a través de [litellm](https://docs.litellm.ai/docs/providers).
  - Soporte para modelos de código abierto como Qwen
  - Interfaz API compatible con OpenAI
  - Sistema LLM de múltiples niveles para diferentes complejidades de tareas

### Herramientas e Integraciones MCP

- 🔍 **Búsqueda y Recuperación**

  - Búsqueda web a través de Tavily, Brave Search y más
  - Rastreo con Jina
  - Extracción avanzada de contenido

- 🔗 **Integración Perfecta con MCP**
  - Amplía capacidades para acceso a dominio privado, gráfico de conocimiento, navegación web y más
  - Facilita la integración de diversas herramientas y metodologías de investigación

### Colaboración Humana

- 🧠 **Humano en el Bucle**

  - Soporta modificación interactiva de planes de investigación usando lenguaje natural
  - Soporta aceptación automática de planes de investigación

- 📝 **Post-Edición de Informes**
  - Soporta edición de bloques tipo Notion
  - Permite refinamientos por IA, incluyendo pulido asistido por IA, acortamiento y expansión de oraciones
  - Impulsado por [tiptap](https://tiptap.dev/)

### Creación de Contenido

- 🎙️ **Generación de Podcasts y Presentaciones**
  - Generación de guiones de podcast y síntesis de audio impulsadas por IA
  - Creación automatizada de presentaciones PowerPoint simples
  - Plantillas personalizables para contenido a medida

## Arquitectura

DeerFlow implementa una arquitectura modular de sistema multi-agente diseñada para investigación automatizada y análisis de código. El sistema está construido sobre LangGraph, permitiendo un flujo de trabajo flexible basado en estados donde los componentes se comunican a través de un sistema de paso de mensajes bien definido.

![Diagrama de Arquitectura](./assets/architecture.png)

> Vélo en vivo en [deerflow.tech](https://deerflow.tech/#multi-agent-architecture)

El sistema emplea un flujo de trabajo racionalizado con los siguientes componentes:

1. **Coordinador**: El punto de entrada que gestiona el ciclo de vida del flujo de trabajo

   - Inicia el proceso de investigación basado en la entrada del usuario
   - Delega tareas al planificador cuando corresponde
   - Actúa como la interfaz principal entre el usuario y el sistema

2. **Planificador**: Componente estratégico para descomposición y planificación de tareas

   - Analiza objetivos de investigación y crea planes de ejecución estructurados
   - Determina si hay suficiente contexto disponible o si se necesita más investigación
   - Gestiona el flujo de investigación y decide cuándo generar el informe final

3. **Equipo de Investigación**: Una colección de agentes especializados que ejecutan el plan:

   - **Investigador**: Realiza búsquedas web y recopilación de información utilizando herramientas como motores de búsqueda web, rastreo e incluso servicios MCP.
   - **Programador**: Maneja análisis de código, ejecución y tareas técnicas utilizando la herramienta Python REPL.
     Cada agente tiene acceso a herramientas específicas optimizadas para su rol y opera dentro del marco LangGraph

4. **Reportero**: Procesador de etapa final para los resultados de la investigación
   - Agrega hallazgos del equipo de investigación
   - Procesa y estructura la información recopilada
   - Genera informes de investigación completos

## Integración de Texto a Voz

DeerFlow ahora incluye una función de Texto a Voz (TTS) que te permite convertir informes de investigación a voz. Esta función utiliza la API TTS de volcengine para generar audio de alta calidad a partir de texto. Características como velocidad, volumen y tono también son personalizables.

### Usando la API TTS

Puedes acceder a la funcionalidad TTS a través del punto final `/api/tts`:

```bash
# Ejemplo de llamada API usando curl
curl --location 'http://localhost:8000/api/tts' \
--header 'Content-Type: application/json' \
--data '{
    "text": "Esto es una prueba de la funcionalidad de texto a voz.",
    "speed_ratio": 1.0,
    "volume_ratio": 1.0,
    "pitch_ratio": 1.0
}' \
--output speech.mp3
```

## Desarrollo

### Pruebas

Ejecuta el conjunto de pruebas:

```bash
# Ejecutar todas las pruebas
make test

# Ejecutar archivo de prueba específico
pytest tests/integration/test_workflow.py

# Ejecutar con cobertura
make coverage
```

### Calidad del Código

```bash
# Ejecutar linting
make lint

# Formatear código
make format
```

### Depuración con LangGraph Studio

DeerFlow utiliza LangGraph para su arquitectura de flujo de trabajo. Puedes usar LangGraph Studio para depurar y visualizar el flujo de trabajo en tiempo real.

#### Ejecutando LangGraph Studio Localmente

DeerFlow incluye un archivo de configuración `langgraph.json` que define la estructura del grafo y las dependencias para LangGraph Studio. Este archivo apunta a los grafos de flujo de trabajo definidos en el proyecto y carga automáticamente variables de entorno desde el archivo `.env`.

##### Mac

```bash
# Instala el gestor de paquetes uv si no lo tienes
curl -LsSf https://astral.sh/uv/install.sh | sh

# Instala dependencias e inicia el servidor LangGraph
uvx --refresh --from "langgraph-cli[inmem]" --with-editable . --python 3.12 langgraph dev --allow-blocking
```

##### Windows / Linux

```bash
# Instalar dependencias
pip install -e .
pip install -U "langgraph-cli[inmem]"

# Iniciar el servidor LangGraph
langgraph dev
```

Después de iniciar el servidor LangGraph, verás varias URLs en la terminal:

- API: <http://127.0.0.1:2024>
- UI de Studio: <https://smith.langchain.com/studio/?baseUrl=http://127.0.0.1:2024>
- Docs de API: <http://127.0.0.1:2024/docs>

Abre el enlace de UI de Studio en tu navegador para acceder a la interfaz de depuración.

#### Usando LangGraph Studio

En la UI de Studio, puedes:

1. Visualizar el grafo de flujo de trabajo y ver cómo se conectan los componentes
2. Rastrear la ejecución en tiempo real para ver cómo fluyen los datos a través del sistema
3. Inspeccionar el estado en cada paso del flujo de trabajo
4. Depurar problemas examinando entradas y salidas de cada componente
5. Proporcionar retroalimentación durante la fase de planificación para refinar planes de investigación

Cuando envías un tema de investigación en la UI de Studio, podrás ver toda la ejecución del flujo de trabajo, incluyendo:

- La fase de planificación donde se crea el plan de investigación
- El bucle de retroalimentación donde puedes modificar el plan
- Las fases de investigación y escritura para cada sección
- La generación del informe final

### Habilitando el Rastreo de LangSmith

DeerFlow soporta el rastreo de LangSmith para ayudarte a depurar y monitorear tus flujos de trabajo. Para habilitar el rastreo de LangSmith:

1. Asegúrate de que tu archivo `.env` tenga las siguientes configuraciones (ver `.env.example`):

   ```bash
   LANGSMITH_TRACING=true
   LANGSMITH_ENDPOINT="https://api.smith.langchain.com"
   LANGSMITH_API_KEY="xxx"
   LANGSMITH_PROJECT="xxx"
   ```

2. Inicia el rastreo y visualiza el grafo localmente con LangSmith ejecutando:

   ```bash
   langgraph dev
   ```

Esto habilitará la visualización de rastros en LangGraph Studio y enviará tus rastros a LangSmith para monitoreo y análisis.

## Docker

También puedes ejecutar este proyecto con Docker.

Primero, necesitas leer la [configuración](docs/configuration_guide.md) a continuación. Asegúrate de que los archivos `.env` y `.conf.yaml` estén listos.

Segundo, para construir una imagen Docker de tu propio servidor web:

```bash
docker build -t deer-flow-api .
```

Finalmente, inicia un contenedor Docker que ejecute el servidor web:

```bash
# Reemplaza deer-flow-api-app con tu nombre de contenedor preferido
docker run -d -t -p 8000:8000 --env-file .env --name deer-flow-api-app deer-flow-api

# detener el servidor
docker stop deer-flow-api-app
```

### Docker Compose (incluye tanto backend como frontend)

DeerFlow proporciona una configuración docker-compose para ejecutar fácilmente tanto el backend como el frontend juntos:

```bash
# construir imagen docker
docker compose build

# iniciar el servidor
docker compose up
```

## Ejemplos

Los siguientes ejemplos demuestran las capacidades de DeerFlow:

### Informes de Investigación

1. **Informe sobre OpenAI Sora** - Análisis de la herramienta IA Sora de OpenAI

   - Discute características, acceso, ingeniería de prompts, limitaciones y consideraciones éticas
   - [Ver informe completo](examples/openai_sora_report.md)

2. **Informe sobre el Protocolo Agent to Agent de Google** - Visión general del protocolo Agent to Agent (A2A) de Google

   - Discute su papel en la comunicación de agentes IA y su relación con el Model Context Protocol (MCP) de Anthropic
   - [Ver informe completo](examples/what_is_agent_to_agent_protocol.md)

3. **¿Qué es MCP?** - Un análisis completo del término "MCP" en múltiples contextos

   - Explora Model Context Protocol en IA, Fosfato Monocálcico en química y Placa de Microcanales en electrónica
   - [Ver informe completo](examples/what_is_mcp.md)

4. **Fluctuaciones del Precio de Bitcoin** - Análisis de los movimientos recientes del precio de Bitcoin

   - Examina tendencias del mercado, influencias regulatorias e indicadores técnicos
   - Proporciona recomendaciones basadas en datos históricos
   - [Ver informe completo](examples/bitcoin_price_fluctuation.md)

5. **¿Qué es LLM?** - Una exploración en profundidad de los Modelos de Lenguaje Grandes

   - Discute arquitectura, entrenamiento, aplicaciones y consideraciones éticas
   - [Ver informe completo](examples/what_is_llm.md)

6. **¿Cómo usar Claude para Investigación Profunda?** - Mejores prácticas y flujos de trabajo para usar Claude en investigación profunda

   - Cubre ingeniería de prompts, análisis de datos e integración con otras herramientas
   - [Ver informe completo](examples/how_to_use_claude_deep_research.md)

7. **Adopción de IA en Salud: Factores de Influencia** - Análisis de factores que impulsan la adopción de IA en salud

   - Discute tecnologías IA, calidad de datos, consideraciones éticas, evaluaciones económicas, preparación organizativa e infraestructura digital
   - [Ver informe completo](examples/AI_adoption_in_healthcare.md)

8. **Impacto de la Computación Cuántica en la Criptografía** - Análisis del impacto de la computación cuántica en la criptografía

   - Discute vulnerabilidades de la criptografía clásica, criptografía post-cuántica y soluciones criptográficas resistentes a la cuántica
   - [Ver informe completo](examples/Quantum_Computing_Impact_on_Cryptography.md)

9. **Aspectos Destacados del Rendimiento de Cristiano Ronaldo** - Análisis de los aspectos destacados del rendimiento de Cristiano Ronaldo
   - Discute sus logros profesionales, goles internacionales y rendimiento en varios partidos
   - [Ver informe completo](examples/Cristiano_Ronaldo's_Performance_Highlights.md)

Para ejecutar estos ejemplos o crear tus propios informes de investigación, puedes usar los siguientes comandos:

```bash
# Ejecutar con una consulta específica
uv run main.py "¿Qué factores están influyendo en la adopción de IA en salud?"

# Ejecutar con parámetros de planificación personalizados
uv run main.py --max_plan_iterations 3 "¿Cómo impacta la computación cuántica en la criptografía?"

# Ejecutar en modo interactivo con preguntas integradas
uv run main.py --interactive

# O ejecutar con prompt interactivo básico
uv run main.py

# Ver todas las opciones disponibles
uv run main.py --help
```

### Modo Interactivo

La aplicación ahora soporta un modo interactivo con preguntas integradas tanto en inglés como en chino:

1. Lanza el modo interactivo:

   ```bash
   uv run main.py --interactive
   ```

2. Selecciona tu idioma preferido (English o 中文)

3. Elige de una lista de preguntas integradas o selecciona la opción para hacer tu propia pregunta

4. El sistema procesará tu pregunta y generará un informe de investigación completo

### Humano en el Bucle

DeerFlow incluye un mecanismo de humano en el bucle que te permite revisar, editar y aprobar planes de investigación antes de que sean ejecutados:

1. **Revisión del Plan**: Cuando el humano en el bucle está habilitado, el sistema presentará el plan de investigación generado para tu revisión antes de la ejecución

2. **Proporcionando Retroalimentación**: Puedes:

   - Aceptar el plan respondiendo con `[ACCEPTED]`
   - Editar el plan proporcionando retroalimentación (p.ej., `[EDIT PLAN] Añadir más pasos sobre implementación técnica`)
   - El sistema incorporará tu retroalimentación y generará un plan revisado

3. **Auto-aceptación**: Puedes habilitar la auto-aceptación para omitir el proceso de revisión:

   - Vía API: Establece `auto_accepted_plan: true` en tu solicitud

4. **Integración API**: Cuando uses la API, puedes proporcionar retroalimentación a través del parámetro `feedback`:

   ```json
   {
     "messages": [{ "role": "user", "content": "¿Qué es la computación cuántica?" }],
     "thread_id": "my_thread_id",
     "auto_accepted_plan": false,
     "feedback": "[EDIT PLAN] Incluir más sobre algoritmos cuánticos"
   }
   ```

### Argumentos de Línea de Comandos

La aplicación soporta varios argumentos de línea de comandos para personalizar su comportamiento:

- **query**: La consulta de investigación a procesar (puede ser múltiples palabras)
- **--interactive**: Ejecutar en modo interactivo con preguntas integradas
- **--max_plan_iterations**: Número máximo de ciclos de planificación (predeterminado: 1)
- **--max_step_num**: Número máximo de pasos en un plan de investigación (predeterminado: 3)
- **--debug**: Habilitar registro detallado de depuración

## Preguntas Frecuentes

Por favor, consulta [FAQ.md](docs/FAQ.md) para más detalles.

## Licencia

Este proyecto es de código abierto y está disponible bajo la [Licencia MIT](./LICENSE).

## Agradecimientos

DeerFlow está construido sobre el increíble trabajo de la comunidad de código abierto. Estamos profundamente agradecidos a todos los proyectos y contribuyentes cuyos esfuerzos han hecho posible DeerFlow. Verdaderamente, nos apoyamos en hombros de gigantes.

Nos gustaría extender nuestro sincero agradecimiento a los siguientes proyectos por sus invaluables contribuciones:

- **[LangChain](https://github.com/langchain-ai/langchain)**: Su excepcional marco impulsa nuestras interacciones y cadenas LLM, permitiendo integración y funcionalidad sin problemas.
- **[LangGraph](https://github.com/langchain-ai/langgraph)**: Su enfoque innovador para la orquestación multi-agente ha sido instrumental en permitir los sofisticados flujos de trabajo de DeerFlow.

Estos proyectos ejemplifican el poder transformador de la colaboración de código abierto, y estamos orgullosos de construir sobre sus cimientos.

### Contribuyentes Clave

Un sentido agradecimiento va para los autores principales de `DeerFlow`, cuya visión, pasión y dedicación han dado vida a este proyecto:

- **[Daniel Walnut](https://github.com/hetaoBackend/)**
- **[Henry Li](https://github.com/magiccube/)**

Su compromiso inquebrantable y experiencia han sido la fuerza impulsora detrás del éxito de DeerFlow. Nos sentimos honrados de tenerlos al timón de este viaje.

## Historial de Estrellas

[![Gráfico de Historial de Estrellas](https://api.star-history.com/svg?repos=bytedance/deer-flow&type=Date)](https://star-history.com/#bytedance/deer-flow&Date)
